/**
 * Generic Workflow Executor Usage Examples
 * 
 * This file demonstrates how to use the new GenericWorkflowExecutor service
 * to replace the old PolicyListWorkflowExecutor and PolicyDetailsWorkflowExecutor classes.
 */

import { GenericWorkflowExecutor } from './generic-workflow-executor.service';
import { WorkflowType } from './generic-workflow-executor.types';
import type {
  WorkflowExecutionParams,
  RawPolicyListWorkflowData,
  RawPolicyDetailsWorkflowData
} from './generic-workflow-executor.types';

/**
 * Example 1: Execute Policy List Workflow
 * Replaces: new PolicyListWorkflowExecutor().executeWorkflow(customerId)
 */
export async function executePolicyListWorkflow(customerId: string): Promise<RawPolicyListWorkflowData> {
  const executor = new GenericWorkflowExecutor();
  
  const params: WorkflowExecutionParams = {
    customerId
  };

  return await executor.executeWorkflow(WorkflowType.POLICY_LIST, params);
}

/**
 * Example 2: Execute Policy Details Workflow
 * Replaces: new PolicyDetailsWorkflowExecutor().executeWorkflow(customerId, memberCode)
 */
export async function executePolicyDetailsWorkflow(
  customerId: string, 
  memberCode: string
): Promise<RawPolicyDetailsWorkflowData> {
  const executor = new GenericWorkflowExecutor();
  
  const params: WorkflowExecutionParams = {
    customerId,
    memberCode
  };

  return await executor.executeWorkflow(WorkflowType.POLICY_DETAILS, params);
}

/**
 * Example 3: Execute with Custom Configuration
 * Shows how to customize the executor behavior
 */
export async function executeWithCustomConfig(customerId: string): Promise<RawPolicyListWorkflowData> {
  const executor = new GenericWorkflowExecutor({
    baseTimeout: 15000,        // 15 seconds timeout
    enableLogging: true,       // Enable detailed logging
  });
  
  return await executor.executeWorkflow(WorkflowType.POLICY_LIST, { customerId });
}

/**
 * Example 4: Execute with Additional Context
 * Shows how to pass additional context data to the workflow
 */
export async function executeWithAdditionalContext(
  customerId: string,
  memberCode: string,
  additionalData: Record<string, any>
): Promise<RawPolicyDetailsWorkflowData> {
  const executor = new GenericWorkflowExecutor();
  
  const params: WorkflowExecutionParams = {
    customerId,
    memberCode,
    additionalContext: additionalData
  };

  return await executor.executeWorkflow(WorkflowType.POLICY_DETAILS, params);
}

/**
 * Example 5: Error Handling Pattern
 * Shows proper error handling with the generic executor
 */
export async function executeWithErrorHandling(customerId: string): Promise<RawPolicyListWorkflowData | null> {
  try {
    const executor = new GenericWorkflowExecutor();
    
    const result = await executor.executeWorkflow(WorkflowType.POLICY_LIST, { customerId });
    
    console.log('Workflow executed successfully:', {
      customer_id: result.customer_id,
      execution_id: result.execution_id,
      policies_count: result.policy_list_data.ListOfPolicyListSocial.length,
      execution_time: result.execution_metadata.total_execution_time_ms
    });
    
    return result;
    
  } catch (error) {
    console.error('Workflow execution failed:', error);
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('HTTP 401')) {
        console.error('Authentication failed - check credentials');
      } else if (error.message.includes('HTTP 500')) {
        console.error('Server error - try again later');
      } else {
        console.error('Workflow error:', error.message);
      }
    }
    
    return null;
  }
}

/**
 * Example 6: Batch Processing
 * Shows how to process multiple workflows efficiently
 */
export async function executeBatchPolicyList(customerIds: string[]): Promise<RawPolicyListWorkflowData[]> {
  const executor = new GenericWorkflowExecutor({ enableLogging: false });
  const results: RawPolicyListWorkflowData[] = [];
  
  for (const customerId of customerIds) {
    try {
      const result = await executor.executeWorkflow(WorkflowType.POLICY_LIST, { customerId });
      results.push(result);
    } catch (error) {
      console.error(`Failed to process customer ${customerId}:`, error);
      // Continue with next customer
    }
  }
  
  return results;
}

/**
 * Example 7: Migration Helper
 * Helper function to migrate from old services to new generic service
 */
export class WorkflowMigrationHelper {
  private executor: GenericWorkflowExecutor;
  
  constructor() {
    this.executor = new GenericWorkflowExecutor();
  }
  
  /**
   * Replaces PolicyListWorkflowExecutor.executeWorkflow()
   */
  async getPolicyList(customerId: string): Promise<RawPolicyListWorkflowData> {
    return await this.executor.executeWorkflow(WorkflowType.POLICY_LIST, { customerId });
  }
  
  /**
   * Replaces PolicyDetailsWorkflowExecutor.executeWorkflow()
   */
  async getPolicyDetails(customerId: string, memberCode: string): Promise<RawPolicyDetailsWorkflowData> {
    return await this.executor.executeWorkflow(WorkflowType.POLICY_DETAILS, { customerId, memberCode });
  }
}

/**
 * Example 8: Type-Safe Workflow Execution
 * Shows how to maintain type safety with the generic executor
 */
export class TypeSafeWorkflowService {
  private executor: GenericWorkflowExecutor;
  
  constructor() {
    this.executor = new GenericWorkflowExecutor();
  }
  
  async executePolicyListWorkflow(customerId: string): Promise<RawPolicyListWorkflowData> {
    // TypeScript ensures we get the correct return type
    return await this.executor.executeWorkflow(WorkflowType.POLICY_LIST, { customerId });
  }
  
  async executePolicyDetailsWorkflow(customerId: string, memberCode: string): Promise<RawPolicyDetailsWorkflowData> {
    // TypeScript ensures we get the correct return type
    return await this.executor.executeWorkflow(WorkflowType.POLICY_DETAILS, { customerId, memberCode });
  }
}
