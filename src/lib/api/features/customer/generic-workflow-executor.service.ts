/**
 * Generic Workflow Executor Service
 *
 * A consolidated, generic workflow executor that can handle multiple workflow types:
 * - Policy List Workflow (3 steps: token, verify, fetch list)
 * - Policy Details Workflow (3 steps: token, verify, fetch details)
 * - Future workflow types can be easily added
 *
 * This service replaces the separate PolicyListWorkflowExecutor and PolicyDetailsWorkflowExecutor
 * classes, eliminating code duplication while maintaining type safety and extensibility.
 */

import type {
  SimplifiedPolicyClaimsWorkflowConfig,
  WorkflowStep,
  WorkflowExecutionContext,
  StepExecutionResult,
  WorkflowExecutionParams,
  WorkflowResultMap,
  RawPolicyListWorkflowData,
  RawPolicyDetailsWorkflowData,
  StepHandlerRegistry,
  ResultBuilderRegistry,
  WorkflowExecutorOptions,
  GenericWorkflowExecutionContext
} from './generic-workflow-executor.types';

import { WorkflowType } from './generic-workflow-executor.types';

import {
  createExecutionContext,
  resolveTemplateVariables,
  getStepsInOrder,
  resolveDataSource
} from './generic-workflow-executor.utils';

// Import workflow configurations
import policyListWorkflowConfig from './policy-list-workflow.json';
import policyDetailsWorkflowConfig from './policy-details-workflow.json';

export class GenericWorkflowExecutor {
  private baseTimeout: number;
  private enableLogging: boolean;
  private stepHandlers: StepHandlerRegistry;
  private resultBuilders: ResultBuilderRegistry;

  constructor(options: WorkflowExecutorOptions = {}) {
    this.baseTimeout = options.baseTimeout || 10000; // 10 seconds default
    this.enableLogging = options.enableLogging !== false; // Default to true
    
    // Initialize step handlers
    this.stepHandlers = {
      get_bearer_token: this.getBearerToken.bind(this),
      verify_citizen_id: this.verifyCitizenId.bind(this),
      fetch_policy_list: this.fetchPolicyList.bind(this),
      fetch_policy_details: this.fetchPolicyDetails.bind(this),
      ...options.customStepHandlers
    };

    // Initialize result builders
    this.resultBuilders = {
      [WorkflowType.POLICY_LIST]: this.buildPolicyListResult.bind(this),
      [WorkflowType.POLICY_DETAILS]: this.buildPolicyDetailsResult.bind(this),
      ...options.customResultBuilders
    };
  }

  /**
   * Execute a workflow of the specified type
   */
  async executeWorkflow<T extends WorkflowType>(
    workflowType: T,
    params: WorkflowExecutionParams
  ): Promise<WorkflowResultMap[T]> {
    const config = this.loadWorkflowConfig(workflowType);
    const context = this.createGenericExecutionContext(workflowType, params);
    const steps = getStepsInOrder(config);
    const stepResults: StepExecutionResult[] = [];

    if (this.enableLogging) {
      console.log(`Starting ${workflowType} workflow execution`, {
        customer_id: params.customerId,
        member_code: params.memberCode,
        execution_id: context.execution_id,
        total_steps: steps.length
      });
    }

    try {
      // Resolve data source based on configuration mode
      const dataSource = resolveDataSource(config);
      context.database_data = {
        social_id: dataSource.social_id,
        channel_id: dataSource.channel_id,
        citizen_id: dataSource.citizen_id
      };

      // Execute each step in sequence
      for (const step of steps) {
        context.current_step = step.id;
        if (this.enableLogging) {
          console.log(`Executing step ${step.id}: ${step.name}`);
        }

        const stepResult = await this.executeStep(step, context);
        stepResults.push(stepResult);

        if (!stepResult.success) {
          throw new Error(`Step ${step.id} (${step.name}) failed: ${stepResult.error_message}`);
        }

        // Store extracted data for use in subsequent steps
        Object.assign(context.step_data, stepResult.extracted_data);
      }

      // Build workflow-specific result
      const result = this.resultBuilders[workflowType](params, context, stepResults);

      if (this.enableLogging) {
        console.log(`${workflowType} workflow execution completed successfully`, {
          customer_id: params.customerId,
          execution_id: context.execution_id,
          total_steps: stepResults.length,
          total_time_ms: result.execution_metadata.total_execution_time_ms
        });
      }

      return result as WorkflowResultMap[T];

    } catch (error) {
      if (this.enableLogging) {
        console.error(`${workflowType} workflow execution failed:`, error);
      }
      throw error;
    }
  }

  /**
   * Load workflow configuration based on type
   */
  private loadWorkflowConfig(workflowType: WorkflowType): SimplifiedPolicyClaimsWorkflowConfig {
    let config: SimplifiedPolicyClaimsWorkflowConfig;

    switch (workflowType) {
      case WorkflowType.POLICY_LIST:
        config = policyListWorkflowConfig as unknown as SimplifiedPolicyClaimsWorkflowConfig;
        break;
      case WorkflowType.POLICY_DETAILS:
        config = policyDetailsWorkflowConfig as unknown as SimplifiedPolicyClaimsWorkflowConfig;
        break;
      default:
        throw new Error(`Unsupported workflow type: ${workflowType}`);
    }

    // Apply environment-specific endpoint resolution
    return {
      ...config,
      steps: config.steps.map(step => ({
        ...step,
        endpoint: this.resolveEndpointForEnvironment(step.endpoint)
      }))
    };
  }

  /**
   * Create execution context with workflow-specific data
   */
  private createGenericExecutionContext(
    workflowType: WorkflowType,
    params: WorkflowExecutionParams
  ): GenericWorkflowExecutionContext {
    const baseContext = createExecutionContext(params.customerId);
    
    // Add workflow-specific context data
    const context: GenericWorkflowExecutionContext = {
      ...baseContext,
      workflow_type: workflowType,
      workflow_params: params
    };

    // Add member_code for policy details workflow
    if (workflowType === WorkflowType.POLICY_DETAILS && params.memberCode) {
      context.step_data.member_code = params.memberCode;
    }

    // Add any additional context data
    if (params.additionalContext) {
      Object.assign(context.step_data, params.additionalContext);
    }

    return context;
  }

  /**
   * Resolve endpoint URL based on environment
   */
  private resolveEndpointForEnvironment(endpoint: string): string {
    const isDevelopment = import.meta.env.DEV;

    if (endpoint.startsWith('/api/tpa')) {
      return endpoint;
    }

    if (isDevelopment && endpoint.includes('thirdpartyadmin.co.th')) {
      const urlParts = endpoint.split('/TPA.TMS.Web.API_PREPROV2');
      if (urlParts.length > 1) {
        return `/api/tpa${urlParts[1]}`;
      }
    }

    return endpoint;
  }

  /**
   * Execute a single workflow step with retry logic
   */
  private async executeStep(step: WorkflowStep, context: WorkflowExecutionContext): Promise<StepExecutionResult> {
    const startTime = Date.now();
    let lastError: Error | null = null;
    let retryCount = 0;

    // Get the appropriate step handler
    const handler = this.stepHandlers[step.name as keyof StepHandlerRegistry];
    if (!handler) {
      throw new Error(`Unknown step: ${step.name}`);
    }

    while (retryCount <= step.retry) {
      try {
        if (this.enableLogging) {
          console.log(`Attempting step ${step.id} (${step.name}), attempt ${retryCount + 1}/${step.retry + 1}`);
        }

        const result = await handler(step, context);

        // Extract data according to step configuration
        const extractedData = this.extractDataFromResponse(result, step.extract);

        // Validate extracted data if validation is specified
        if (step.validate) {
          this.validateStepResult(result, step.validate);
        }

        return {
          step_id: step.id,
          step_name: step.name,
          success: true,
          execution_time_ms: Date.now() - startTime,
          extracted_data: extractedData,
          retry_count: retryCount
        };

      } catch (error) {
        lastError = error as Error;
        retryCount++;
        
        if (this.enableLogging) {
          console.warn(`Step ${step.id} (${step.name}) attempt ${retryCount} failed:`, error);
        }

        if (retryCount <= step.retry) {
          const config = this.loadWorkflowConfig((context as GenericWorkflowExecutionContext).workflow_type);
          const delayMs = config.options.retry_delay_seconds * 1000 * Math.pow(2, retryCount - 1);
          if (this.enableLogging) {
            console.log(`Retrying step ${step.id} in ${delayMs}ms...`);
          }
          await this.delay(delayMs);
        }
      }
    }

    return {
      step_id: step.id,
      step_name: step.name,
      success: false,
      execution_time_ms: Date.now() - startTime,
      extracted_data: {},
      error_message: lastError?.message || 'Unknown error',
      retry_count: retryCount - 1
    };
  }

  /**
   * Step 1: Get Bearer Token
   */
  private async getBearerToken(step: WorkflowStep, context: WorkflowExecutionContext): Promise<any> {
    const requestBody = this.resolveStepRequest(step.request, context);

    if (this.enableLogging) {
      console.log(`Making request to: ${step.endpoint}`, {
        method: step.method,
        body: requestBody
      });
    }

    const response = await fetch(step.endpoint, {
      method: step.method,
      headers: {
        'Content-Type': 'application/json',
        ...step.headers
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(this.baseTimeout)
    });

    if (!response.ok) {
      const errorText = await response.text();
      if (this.enableLogging) {
        console.error(`API request failed:`, {
          status: response.status,
          statusText: response.statusText,
          endpoint: step.endpoint,
          error: errorText
        });
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }

    const responseText = await response.text();
    if (this.enableLogging) {
      console.log(`API response received:`, responseText);
    }

    // The GetToken API returns a plain string token
    return responseText.replace(/"/g, ''); // Remove quotes if present
  }

  /**
   * Step 2: Verify Citizen ID
   */
  private async verifyCitizenId(step: WorkflowStep, context: WorkflowExecutionContext): Promise<any> {
    const requestBody = this.resolveStepRequest(step.request, context);
    const headers = this.resolveStepHeaders(step.headers || {}, context);

    const response = await fetch(step.endpoint, {
      method: step.method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(this.baseTimeout)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Step 3: Fetch Policy List
   */
  private async fetchPolicyList(step: WorkflowStep, context: WorkflowExecutionContext): Promise<any> {
    const requestBody = this.resolveStepRequest(step.request, context);
    const headers = this.resolveStepHeaders(step.headers || {}, context);

    const response = await fetch(step.endpoint, {
      method: step.method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(this.baseTimeout)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Step 3: Fetch Policy Details (for specific member code)
   */
  private async fetchPolicyDetails(step: WorkflowStep, context: WorkflowExecutionContext): Promise<any> {
    const requestBody = this.resolveStepRequest(step.request, context);
    const headers = this.resolveStepHeaders(step.headers || {}, context);

    if (this.enableLogging) {
      console.log(`Fetching policy details for member code: ${context.step_data.member_code}`);
    }

    const response = await fetch(step.endpoint, {
      method: step.method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(this.baseTimeout)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    // Return the result directly - extraction will be handled by extractDataFromResponse
    return result;
  }

  /**
   * Resolve template variables in step request body
   */
  private resolveStepRequest(request: Record<string, any>, context: WorkflowExecutionContext): Record<string, any> {
    const resolved: Record<string, any> = {};

    for (const [key, value] of Object.entries(request)) {
      if (typeof value === 'string') {
        resolved[key] = resolveTemplateVariables(value, context);
      } else {
        resolved[key] = value;
      }
    }

    return resolved;
  }

  /**
   * Resolve template variables in step headers
   */
  private resolveStepHeaders(headers: Record<string, string>, context: WorkflowExecutionContext): Record<string, string> {
    const resolved: Record<string, string> = {};

    for (const [key, value] of Object.entries(headers)) {
      resolved[key] = resolveTemplateVariables(value, context);
    }

    return resolved;
  }

  /**
   * Extract data from API response using JSONPath-like expressions
   */
  private extractDataFromResponse(response: any, extractConfig: Record<string, string>): Record<string, any> {
    const extracted: Record<string, any> = {};

    for (const [key, path] of Object.entries(extractConfig)) {
      if (path === '$') {
        // Root response
        extracted[key] = response;
      } else if (path.startsWith('$.')) {
        // Simple JSONPath extraction
        const pathParts = path.substring(2).split('.');
        let value = response;

        for (const part of pathParts) {
          if (part.includes('[*]')) {
            // Array extraction
            const arrayKey = part.replace('[*]', '');
            if (value[arrayKey] && Array.isArray(value[arrayKey])) {
              const nextPart = pathParts[pathParts.indexOf(part) + 1];
              if (nextPart) {
                value = value[arrayKey].map((item: any) => item[nextPart]).filter(Boolean);
              } else {
                value = value[arrayKey];
              }
              break;
            }
          } else if (value && typeof value === 'object') {
            value = value[part];
          } else {
            value = undefined;
            break;
          }
        }

        extracted[key] = value;
      }
    }

    return extracted;
  }

  /**
   * Validate step result according to validation rules
   */
  private validateStepResult(response: any, validationRule: string): void {
    // Simple validation for citizen ID verification
    if (validationRule.includes("Status == '1'")) {
      const listOfSearchCitizenID = response.ListOfSearchCitizenID;
      if (!listOfSearchCitizenID || !Array.isArray(listOfSearchCitizenID) || listOfSearchCitizenID.length === 0) {
        throw new Error('No citizen ID search results found');
      }

      const firstResult = listOfSearchCitizenID[0];
      if (firstResult.Status !== '1') {
        throw new Error(`Citizen ID verification failed: Status = ${firstResult.Status}`);
      }
    }
  }

  /**
   * Utility method to add delay for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Build result for Policy List workflow
   */
  private buildPolicyListResult(
    params: WorkflowExecutionParams,
    context: WorkflowExecutionContext,
    stepResults: StepExecutionResult[]
  ): RawPolicyListWorkflowData {
    const completedAt = new Date();

    return {
      customer_id: params.customerId,
      execution_id: context.execution_id,
      policy_list_data: {
        ListOfPolicyListSocial: context.step_data.policies || []
      },
      member_codes: context.step_data.member_codes || [],
      execution_metadata: {
        started_at: context.started_at,
        completed_at: completedAt,
        total_execution_time_ms: completedAt.getTime() - context.started_at.getTime(),
        step_results: stepResults
      }
    };
  }

  /**
   * Build result for Policy Details workflow
   */
  private buildPolicyDetailsResult(
    params: WorkflowExecutionParams,
    context: WorkflowExecutionContext,
    stepResults: StepExecutionResult[]
  ): RawPolicyDetailsWorkflowData {
    const completedAt = new Date();

    if (!params.memberCode) {
      throw new Error('Member code is required for policy details workflow');
    }

    return {
      customer_id: params.customerId,
      member_code: params.memberCode,
      execution_id: context.execution_id,
      policy_details_data: {
        ListOfPolDet: context.step_data.policy_details || [],
        ListOfPolClaim: context.step_data.claims_data || []
      },
      execution_metadata: {
        started_at: context.started_at,
        completed_at: completedAt,
        total_execution_time_ms: completedAt.getTime() - context.started_at.getTime(),
        step_results: stepResults
      }
    };
  }
}

// Export the WorkflowType enum for convenience
export { WorkflowType } from './generic-workflow-executor.types';

// Export the result types for backward compatibility
export type {
  RawPolicyListWorkflowData,
  RawPolicyDetailsWorkflowData,
  WorkflowExecutionParams,
  WorkflowExecutorOptions
} from './generic-workflow-executor.types';
