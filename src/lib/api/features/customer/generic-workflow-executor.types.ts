/**
 * Generic Workflow Executor Types
 * 
 * Defines types and interfaces for the generic workflow executor service
 * that can handle multiple workflow types (policy list, policy details, etc.)
 */

import type {
  SimplifiedPolicyClaimsWorkflowConfig,
  WorkflowExecutionContext,
  StepExecutionResult
} from './policy-claims-workflow.types';

/**
 * Supported workflow types
 */
export enum WorkflowType {
  POLICY_LIST = 'policy_list',
  POLICY_DETAILS = 'policy_details'
}

/**
 * Workflow configuration mapping
 */
export interface WorkflowConfigMap {
  [WorkflowType.POLICY_LIST]: SimplifiedPolicyClaimsWorkflowConfig;
  [WorkflowType.POLICY_DETAILS]: SimplifiedPolicyClaimsWorkflowConfig;
}

/**
 * Execution parameters for different workflow types
 */
export interface WorkflowExecutionParams {
  customerId: string;
  memberCode?: string; // Required for POLICY_DETAILS, optional for POLICY_LIST
  additionalContext?: Record<string, any>;
}

/**
 * Base interface for all workflow execution results
 */
export interface BaseWorkflowExecutionResult {
  customer_id: string;
  execution_id: string;
  execution_metadata: {
    started_at: Date;
    completed_at: Date;
    total_execution_time_ms: number;
    step_results: StepExecutionResult[];
  };
}

/**
 * Policy List workflow result (preserving original interface)
 */
export interface RawPolicyListWorkflowData extends BaseWorkflowExecutionResult {
  policy_list_data: {
    ListOfPolicyListSocial: any[];
  };
  member_codes: string[];
}

/**
 * Policy Details workflow result (preserving original interface)
 */
export interface RawPolicyDetailsWorkflowData extends BaseWorkflowExecutionResult {
  member_code: string;
  policy_details_data: {
    ListOfPolDet: any[];
    ListOfPolClaim: any[];
  };
}

/**
 * Union type for all possible workflow results
 */
export type WorkflowExecutionResult = RawPolicyListWorkflowData | RawPolicyDetailsWorkflowData;

/**
 * Type mapping for workflow results
 */
export interface WorkflowResultMap {
  [WorkflowType.POLICY_LIST]: RawPolicyListWorkflowData;
  [WorkflowType.POLICY_DETAILS]: RawPolicyDetailsWorkflowData;
}

/**
 * Step handler function signature
 */
export type StepHandler = (
  step: import('./policy-claims-workflow.types').WorkflowStep,
  context: WorkflowExecutionContext
) => Promise<any>;

/**
 * Step handler registry mapping step names to handler functions
 */
export interface StepHandlerRegistry {
  get_bearer_token: StepHandler;
  verify_citizen_id: StepHandler;
  fetch_policy_list: StepHandler;
  fetch_policy_details: StepHandler;
}

/**
 * Workflow configuration loader function signature
 */
export type WorkflowConfigLoader = (workflowType: WorkflowType) => SimplifiedPolicyClaimsWorkflowConfig;

/**
 * Result builder function signature for creating workflow-specific results
 */
export type ResultBuilder<T extends WorkflowExecutionResult> = (
  params: WorkflowExecutionParams,
  context: WorkflowExecutionContext,
  stepResults: StepExecutionResult[]
) => T;

/**
 * Result builder registry mapping workflow types to result builders
 */
export interface ResultBuilderRegistry {
  [WorkflowType.POLICY_LIST]: ResultBuilder<RawPolicyListWorkflowData>;
  [WorkflowType.POLICY_DETAILS]: ResultBuilder<RawPolicyDetailsWorkflowData>;
}

/**
 * Generic workflow executor options
 */
export interface WorkflowExecutorOptions {
  baseTimeout?: number;
  enableLogging?: boolean;
  customStepHandlers?: Partial<StepHandlerRegistry>;
  customResultBuilders?: Partial<ResultBuilderRegistry>;
}

/**
 * Workflow execution context with additional generic fields
 */
export interface GenericWorkflowExecutionContext extends WorkflowExecutionContext {
  workflow_type: WorkflowType;
  workflow_params: WorkflowExecutionParams;
}
