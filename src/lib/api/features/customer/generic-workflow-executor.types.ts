/**
 * Generic Workflow Executor Types
 *
 * Consolidated types and interfaces for the generic workflow executor service
 * that can handle multiple workflow types (policy list, policy details, etc.)
 *
 * This file consolidates types from the original policy-claims-workflow.types.ts
 * to follow the generic workflow pattern.
 */

// ============================================================================
// CORE WORKFLOW CONFIGURATION TYPES
// ============================================================================

export interface FixedValues {
  social_id: string;
  channel_id: string;
  citizen_id: string;
}

export interface DatabaseConfig {
  table: string;
  fields: {
    social_id: string;
    channel_id: string;
  };
  where: string;
}

export interface WorkflowConfig {
  mode: 'database' | 'fixed' | 'hybrid';
  fixed_values: FixedValues;
  database: DatabaseConfig;
}

export interface WorkflowStep {
  id: number;
  name: string;
  endpoint: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  request: Record<string, any>;
  extract: Record<string, string>;
  validate?: string;
  iterate_over?: string;
  retry: number;
}

export interface StorageConfig {
  table: string;
  key: string[];
  data: Record<string, string>;
}

export interface WorkflowOptions {
  timeout_minutes: number;
  retry_delay_seconds: number;
}

export interface SimplifiedPolicyClaimsWorkflowConfig {
  name: string;
  version: string;
  config: WorkflowConfig;
  steps: WorkflowStep[];
  storage: StorageConfig;
  options: WorkflowOptions;
}

// ============================================================================
// RUNTIME EXECUTION TYPES
// ============================================================================

export interface WorkflowExecutionContext {
  customer_id: string;
  execution_id: string;
  started_at: Date;
  current_step: number;
  step_data: Record<string, any>;
  database_data: Record<string, any>;
}

export interface StepExecutionResult {
  step_id: number;
  step_name: string;
  success: boolean;
  execution_time_ms: number;
  extracted_data: Record<string, any>;
  error_message?: string;
  retry_count: number;
}

export interface GenericWorkflowExecutionResult {
  workflow_name: string;
  execution_id: string;
  customer_id: string;
  success: boolean;
  total_execution_time_ms: number;
  started_at: Date;
  completed_at: Date;
  step_results: StepExecutionResult[];
  final_data: Record<string, any>;
  error_message?: string;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface GetTokenResponse {
  token: string;
}

export interface SearchCitizenIDResponse {
  ListOfSearchCitizenID: Array<{
    Status: string;
    CitizenID: string;
  }>;
  ErrorMessage: string;
}

export interface PolicyListSocialResponse {
  ListOfPolicyListSocial: Array<{
    Name: string;
    CitizenID: string;
    PolNo: string;
    MemberCode: string;
    EffFrom: string;
    EffTo: string;
  }>;
}

export interface PolicyDetailSocialResponse {
  ListOfPolDet: any; // Policy details structure may vary
  ListOfPolClaim: any; // Claims data structure may vary
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type TemplateVariable =
  | `{{database.${string}}}`
  | `{{step_${number}.${string}}}`
  | `{{iteration.${string}}}`
  | `{{input.${string}}}`
  | `{{current_timestamp}}`;

export interface WorkflowValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

// ============================================================================
// GENERIC WORKFLOW EXECUTOR TYPES
// ============================================================================

/**
 * Supported workflow types
 */
export enum WorkflowType {
  POLICY_LIST = 'policy_list',
  POLICY_DETAILS = 'policy_details'
}

/**
 * Workflow configuration mapping
 */
export interface WorkflowConfigMap {
  [WorkflowType.POLICY_LIST]: SimplifiedPolicyClaimsWorkflowConfig;
  [WorkflowType.POLICY_DETAILS]: SimplifiedPolicyClaimsWorkflowConfig;
}

/**
 * Execution parameters for different workflow types
 */
export interface WorkflowExecutionParams {
  customerId: string;
  memberCode?: string; // Required for POLICY_DETAILS, optional for POLICY_LIST
  additionalContext?: Record<string, any>;
}

/**
 * Base interface for all workflow execution results
 */
export interface BaseWorkflowExecutionResult {
  customer_id: string;
  execution_id: string;
  execution_metadata: {
    started_at: Date;
    completed_at: Date;
    total_execution_time_ms: number;
    step_results: StepExecutionResult[];
  };
}

/**
 * Policy List workflow result (preserving original interface)
 */
export interface RawPolicyListWorkflowData extends BaseWorkflowExecutionResult {
  policy_list_data: {
    ListOfPolicyListSocial: any[];
  };
  member_codes: string[];
}

/**
 * Policy Details workflow result (preserving original interface)
 */
export interface RawPolicyDetailsWorkflowData extends BaseWorkflowExecutionResult {
  member_code: string;
  policy_details_data: {
    ListOfPolDet: any[];
    ListOfPolClaim: any[];
  };
}

/**
 * Union type for all possible workflow results
 */
export type WorkflowExecutionResult = RawPolicyListWorkflowData | RawPolicyDetailsWorkflowData;

/**
 * Type mapping for workflow results
 */
export interface WorkflowResultMap {
  [WorkflowType.POLICY_LIST]: RawPolicyListWorkflowData;
  [WorkflowType.POLICY_DETAILS]: RawPolicyDetailsWorkflowData;
}

/**
 * Step handler function signature
 */
export type StepHandler = (
  step: WorkflowStep,
  context: WorkflowExecutionContext
) => Promise<any>;

/**
 * Step handler registry mapping step names to handler functions
 */
export interface StepHandlerRegistry {
  get_bearer_token: StepHandler;
  verify_citizen_id: StepHandler;
  fetch_policy_list: StepHandler;
  fetch_policy_details: StepHandler;
}

/**
 * Workflow configuration loader function signature
 */
export type WorkflowConfigLoader = (workflowType: WorkflowType) => SimplifiedPolicyClaimsWorkflowConfig;

/**
 * Result builder function signature for creating workflow-specific results
 */
export type ResultBuilder<T extends BaseWorkflowExecutionResult> = (
  params: WorkflowExecutionParams,
  context: WorkflowExecutionContext,
  stepResults: StepExecutionResult[]
) => T;

/**
 * Result builder registry mapping workflow types to result builders
 */
export interface ResultBuilderRegistry {
  [WorkflowType.POLICY_LIST]: ResultBuilder<RawPolicyListWorkflowData>;
  [WorkflowType.POLICY_DETAILS]: ResultBuilder<RawPolicyDetailsWorkflowData>;
}

/**
 * Generic workflow executor options
 */
export interface WorkflowExecutorOptions {
  baseTimeout?: number;
  enableLogging?: boolean;
  customStepHandlers?: Partial<StepHandlerRegistry>;
  customResultBuilders?: Partial<ResultBuilderRegistry>;
}

/**
 * Workflow execution context with additional generic fields
 */
export interface GenericWorkflowExecutionContext extends WorkflowExecutionContext {
  workflow_type: WorkflowType;
  workflow_params: WorkflowExecutionParams;
}
