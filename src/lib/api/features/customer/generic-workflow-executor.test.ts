/**
 * Generic Workflow Executor Service Tests
 * 
 * Basic tests to verify the generic workflow executor works correctly
 * for both policy list and policy details workflows.
 */

import { GenericWorkflowExecutor } from './generic-workflow-executor.service';
import { WorkflowType } from './generic-workflow-executor.types';
import type { WorkflowExecutionParams } from './generic-workflow-executor.types';

// Mock fetch for testing
global.fetch = jest.fn();

describe('GenericWorkflowExecutor', () => {
  let executor: GenericWorkflowExecutor;

  beforeEach(() => {
    executor = new GenericWorkflowExecutor({ enableLogging: false });
    jest.clearAllMocks();
  });

  describe('Policy List Workflow', () => {
    it('should create executor with correct configuration', () => {
      expect(executor).toBeInstanceOf(GenericWorkflowExecutor);
    });

    it('should accept policy list workflow parameters', () => {
      const params: WorkflowExecutionParams = {
        customerId: '12345'
      };

      expect(params.customerId).toBe('12345');
      expect(params.memberCode).toBeUndefined();
    });

    it('should load policy list workflow configuration', () => {
      // Test that the workflow type enum is correctly defined
      expect(WorkflowType.POLICY_LIST).toBe('policy_list');
      expect(WorkflowType.POLICY_DETAILS).toBe('policy_details');
    });
  });

  describe('Policy Details Workflow', () => {
    it('should accept policy details workflow parameters with member code', () => {
      const params: WorkflowExecutionParams = {
        customerId: '12345',
        memberCode: 'MEMBER001'
      };

      expect(params.customerId).toBe('12345');
      expect(params.memberCode).toBe('MEMBER001');
    });

    it('should handle additional context parameters', () => {
      const params: WorkflowExecutionParams = {
        customerId: '12345',
        memberCode: 'MEMBER001',
        additionalContext: {
          custom_field: 'custom_value'
        }
      };

      expect(params.additionalContext).toEqual({
        custom_field: 'custom_value'
      });
    });
  });

  describe('Configuration Loading', () => {
    it('should support both workflow types', () => {
      // Verify that both workflow types are supported
      const policyListType = WorkflowType.POLICY_LIST;
      const policyDetailsType = WorkflowType.POLICY_DETAILS;

      expect(policyListType).toBe('policy_list');
      expect(policyDetailsType).toBe('policy_details');
    });
  });

  describe('Type Safety', () => {
    it('should maintain type safety for workflow results', () => {
      // This test verifies that TypeScript types are correctly defined
      // The actual execution would require mocking the entire workflow
      const params: WorkflowExecutionParams = {
        customerId: '12345'
      };

      // Test that parameters are correctly typed
      expect(typeof params.customerId).toBe('string');
    });
  });

  describe('Error Handling', () => {
    it('should handle missing member code for policy details workflow', () => {
      const params: WorkflowExecutionParams = {
        customerId: '12345'
        // memberCode is missing for policy details workflow
      };

      // This would be caught during execution when memberCode is required
      expect(params.memberCode).toBeUndefined();
    });
  });
});

// Integration test helper functions
export const testHelpers = {
  /**
   * Create test parameters for policy list workflow
   */
  createPolicyListParams: (customerId: string): WorkflowExecutionParams => ({
    customerId
  }),

  /**
   * Create test parameters for policy details workflow
   */
  createPolicyDetailsParams: (customerId: string, memberCode: string): WorkflowExecutionParams => ({
    customerId,
    memberCode
  }),

  /**
   * Create executor with test configuration
   */
  createTestExecutor: (options = {}) => new GenericWorkflowExecutor({
    enableLogging: false,
    baseTimeout: 5000,
    ...options
  })
};
