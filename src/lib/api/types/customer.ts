export interface CustomerInterface {
    id?: number;
    customer_id: number;
    name: string;
    first_name?: string;
    last_name?: string;
	email: string;
    age: number;
    phone: string;
    
    // role: string;
    // partner: string;

    created_by: string;
    created_on: string;
    updated_by: string;
    updated_on: string;
    
    // Check the result from GET API response again
    // gender_id: number;
    // main_interface: number;
    // line_user_id: string;
    line_user?: {
        display_name: string;
    };

    tag?: {
        id: number;
        name: string;
        description: string;
        color: string;
    };
    tags?: Array<{
        id: number;
        name: string;
        description?: string;
        color: string;
    }>;
    platforms?: Array<{
        id: number;
        platform: string;
        name?: string;
        channel_name: string;
		last_interaction: string;
    }> | {
        id: number;
        name: string;
    };
}

export interface CustomerResponse {
    customers: CustomerInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface CustomerTicketsInterface {
    // TODO - Change the type of variables with any type
    customer_id: number;
    customer_name: string;
    tickets: any;
}

export interface CustomerTicketResponse {
    customer_tickets: CustomerTicketsInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface CustomerPoliciesInterface {
    customer_id: number;
    customer_name: string;
    customer_email: string;
    policies: import('$lib/types/customer').Policy[];
    claims: import('$lib/types/customer').Claim[];
    statistics: import('$lib/types/customer').PolicyStatistics;
    last_updated: string;
}

export interface CustomerPoliciesResponse {
    customer_policies: CustomerPoliciesInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface RawCustomerPoliciesInterface {
    customer_id: string;
    execution_id: string;
    policy_list_data: {
        ListOfPolicyListSocial: any[];
    };
    policy_details_data: {
        ListOfPolDet: any[];
        ListOfPolClaim: any[];
    };
    execution_metadata: {
        started_at: Date;
        completed_at: Date;
        total_execution_time_ms: number;
        step_results: any[];
    };
}

export interface RawCustomerPoliciesResponse {
    customer_policies: RawCustomerPoliciesInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}

export interface CustomerNoteInterface {
    id: number;
    created_by_name: string;
    updated_by_name: string;
    content: string;
    is_active: boolean;
    created_on: string; // ISO timestamp
    updated_on: string; // ISO timestamp
    customer: number;
    created_by: number;
    updated_by: number;
};

export interface CustomerNoteResponse {
    customer_notes: CustomerNoteInterface[];
    res_status: number;
    res_msg?: string;
    error_msg?: string;
}