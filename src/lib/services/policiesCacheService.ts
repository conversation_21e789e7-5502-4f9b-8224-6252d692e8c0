/**
 * Policies Cache Service
 *
 * Provides platform-specific caching for customer policies data and policy details with automatic expiration.
 * Each platform's data is cached separately with a 1-hour expiration time.
 */

import type { CustomerPoliciesData } from '$lib/types/customer';
import type { RawPolicyDetailsWorkflowData } from '$lib/api/features/customer/generic-workflow-executor.types';

interface CacheEntry {
    data: CustomerPoliciesData;
    timestamp: number;
    expiresAt: number;
}

interface PolicyDetailsCacheEntry {
    data: RawPolicyDetailsWorkflowData;
    timestamp: number;
    expiresAt: number;
}

interface CacheStats {
    totalEntries: number;
    expiredEntries: number;
    hitRate: number;
    totalRequests: number;
    cacheHits: number;
    policyDetailsEntries: number;
    policyDetailsRequests: number;
    policyDetailsCacheHits: number;
}

export class PoliciesCacheService {
    private static instance: PoliciesCacheService;
    private cache = new Map<string, CacheEntry>();
    private policyDetailsCache = new Map<string, PolicyDetailsCacheEntry>();
    private readonly CACHE_DURATION = 60 * 60 * 1000;
    private stats = {
        totalRequests: 0,
        cacheHits: 0,
        policyDetailsRequests: 0,
        policyDetailsCacheHits: 0
    };

    private constructor() {
        // Start cleanup interval - run every 30 seconds
        setInterval(() => this.cleanup(), 30000);
    }

    /**
     * Get singleton instance
     */
    static getInstance(): PoliciesCacheService {
        if (!PoliciesCacheService.instance) {
            PoliciesCacheService.instance = new PoliciesCacheService();
        }
        return PoliciesCacheService.instance;
    }

    /**
     * Generate cache key for customer and platform
     */
    private getCacheKey(customerId: number, platformId: number | null): string {
        return `policies_${customerId}_${platformId || 'default'}`;
    }

    /**
     * Generate cache key for policy details (customer, member code, and platform)
     */
    private getPolicyDetailsCacheKey(customerId: number, memberCode: string, platformId: number | null): string {
        return `policy_details_${customerId}_${memberCode}_${platformId || 'default'}`;
    }

    /**
     * Check if cache entry is still valid
     */
    private isValid(entry: CacheEntry | PolicyDetailsCacheEntry): boolean {
        return Date.now() < entry.expiresAt;
    }

    /**
     * Get cached policies data if available and not expired
     */
    get(customerId: number, platformId: number | null): CustomerPoliciesData | null {
        this.stats.totalRequests++;
        
        const key = this.getCacheKey(customerId, platformId);
        const entry = this.cache.get(key);

        if (!entry) {
            console.log(`Cache miss for key: ${key}`);
            return null;
        }

        if (!this.isValid(entry)) {
            console.log(`Cache expired for key: ${key}`);
            this.cache.delete(key);
            return null;
        }

        this.stats.cacheHits++;
        console.log(`Cache hit for key: ${key}`);
        return entry.data;
    }

    /**
     * Store policies data in cache with expiration
     */
    set(customerId: number, platformId: number | null, data: CustomerPoliciesData): void {
        const key = this.getCacheKey(customerId, platformId);
        const now = Date.now();
        
        const entry: CacheEntry = {
            data,
            timestamp: now,
            expiresAt: now + this.CACHE_DURATION
        };

        this.cache.set(key, entry);
        console.log(`Cached data for key: ${key}, expires at: ${new Date(entry.expiresAt).toISOString()}`);
    }

    /**
     * Remove specific cache entry
     */
    remove(customerId: number, platformId: number | null): boolean {
        const key = this.getCacheKey(customerId, platformId);
        const deleted = this.cache.delete(key);
        
        if (deleted) {
            console.log(`Removed cache entry for key: ${key}`);
        }
        
        return deleted;
    }

    /**
     * Clear all cache entries
     */
    clear(): void {
        const policyListSize = this.cache.size;
        const policyDetailsSize = this.policyDetailsCache.size;
        this.cache.clear();
        this.policyDetailsCache.clear();
        console.log(`Cleared ${policyListSize} policy list cache entries and ${policyDetailsSize} policy details cache entries`);
    }

    /**
     * Remove expired entries from cache
     */
    private cleanup(): void {
        const now = Date.now();
        let removedCount = 0;

        // Clean up policy list cache
        for (const [key, entry] of this.cache.entries()) {
            if (now >= entry.expiresAt) {
                this.cache.delete(key);
                removedCount++;
            }
        }

        // Clean up policy details cache
        for (const [key, entry] of this.policyDetailsCache.entries()) {
            if (now >= entry.expiresAt) {
                this.policyDetailsCache.delete(key);
                removedCount++;
            }
        }

        if (removedCount > 0) {
            console.log(`Cleaned up ${removedCount} expired cache entries`);
        }
    }

    /**
     * Get cache statistics
     */
    getStats(): CacheStats {
        const now = Date.now();
        let expiredEntries = 0;

        // Count expired policy list entries
        for (const entry of this.cache.values()) {
            if (now >= entry.expiresAt) {
                expiredEntries++;
            }
        }

        // Count expired policy details entries
        for (const entry of this.policyDetailsCache.values()) {
            if (now >= entry.expiresAt) {
                expiredEntries++;
            }
        }

        return {
            totalEntries: this.cache.size,
            expiredEntries,
            hitRate: this.stats.totalRequests > 0 ? (this.stats.cacheHits / this.stats.totalRequests) * 100 : 0,
            totalRequests: this.stats.totalRequests,
            cacheHits: this.stats.cacheHits,
            policyDetailsEntries: this.policyDetailsCache.size,
            policyDetailsRequests: this.stats.policyDetailsRequests,
            policyDetailsCacheHits: this.stats.policyDetailsCacheHits
        };
    }

    /**
     * Check if data exists in cache (regardless of expiration)
     */
    has(customerId: number, platformId: number | null): boolean {
        const key = this.getCacheKey(customerId, platformId);
        return this.cache.has(key);
    }

    /**
     * Get cache entry with metadata (for debugging)
     */
    getEntry(customerId: number, platformId: number | null): CacheEntry | null {
        const key = this.getCacheKey(customerId, platformId);
        return this.cache.get(key) || null;
    }

    /**
     * Force refresh - remove from cache to trigger fresh API call
     */
    forceRefresh(customerId: number, platformId: number | null): void {
        this.remove(customerId, platformId);
        console.log(`Forced refresh for customer ${customerId}, platform ${platformId}`);
    }

    // Policy Details Cache Methods

    /**
     * Get cached policy details data if available and not expired
     */
    getPolicyDetails(customerId: number, memberCode: string, platformId: number | null): RawPolicyDetailsWorkflowData | null {
        this.stats.policyDetailsRequests++;

        const key = this.getPolicyDetailsCacheKey(customerId, memberCode, platformId);
        const entry = this.policyDetailsCache.get(key);

        if (!entry) {
            console.log(`Policy details cache miss for key: ${key}`);
            return null;
        }

        if (!this.isValid(entry)) {
            console.log(`Policy details cache expired for key: ${key}`);
            this.policyDetailsCache.delete(key);
            return null;
        }

        this.stats.policyDetailsCacheHits++;
        console.log(`Policy details cache hit for key: ${key}`);
        return entry.data;
    }

    /**
     * Store policy details data in cache with expiration
     */
    setPolicyDetails(customerId: number, memberCode: string, platformId: number | null, data: RawPolicyDetailsWorkflowData): void {
        const key = this.getPolicyDetailsCacheKey(customerId, memberCode, platformId);
        const now = Date.now();

        const entry: PolicyDetailsCacheEntry = {
            data,
            timestamp: now,
            expiresAt: now + this.CACHE_DURATION
        };

        this.policyDetailsCache.set(key, entry);
        console.log(`Cached policy details for key: ${key}, expires at: ${new Date(entry.expiresAt).toISOString()}`);
    }

    /**
     * Remove specific policy details cache entry
     */
    removePolicyDetails(customerId: number, memberCode: string, platformId: number | null): boolean {
        const key = this.getPolicyDetailsCacheKey(customerId, memberCode, platformId);
        const deleted = this.policyDetailsCache.delete(key);

        if (deleted) {
            console.log(`Removed policy details cache entry for key: ${key}`);
        }

        return deleted;
    }

    /**
     * Force refresh all policy details for a customer and platform - remove from cache to trigger fresh API calls
     */
    forceRefreshAllPolicyDetails(customerId: number, platformId: number | null): void {
        const keyPrefix = `policy_details_${customerId}_`;
        const platformSuffix = `_${platformId || 'default'}`;
        let removedCount = 0;

        for (const key of this.policyDetailsCache.keys()) {
            if (key.startsWith(keyPrefix) && key.endsWith(platformSuffix)) {
                this.policyDetailsCache.delete(key);
                removedCount++;
            }
        }

        console.log(`Forced refresh of ${removedCount} policy details cache entries for customer ${customerId}, platform ${platformId}`);
    }

    /**
     * Force refresh both policy list and all policy details for a customer and platform
     */
    forceRefreshAll(customerId: number, platformId: number | null): void {
        this.forceRefresh(customerId, platformId);
        this.forceRefreshAllPolicyDetails(customerId, platformId);
    }
}

// Export singleton instance
export const policiesCacheService = PoliciesCacheService.getInstance();
